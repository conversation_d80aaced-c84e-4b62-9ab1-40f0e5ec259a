import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  MoreHorizontal,
  Edit,
  UserX,
  UserCheck,
  Shield,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Key,
  Trash,
  Eye,
  EyeOff
} from 'lucide-react';
import { useStaffManagement } from '@/hooks/useStaffManagement';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  division_id: string | null;
  division_name: string | null;
  phone: string | null;
  country: string;
  is_active: boolean;
  requires_password_change: boolean;
  last_password_change: string | null;
  invitation_accepted_at: string | null;
  created_at: string;
}

interface StaffTableProps {
  staffMembers: StaffMember[];
  isLoading: boolean;
  onRefresh: () => void;
}

const StaffTable: React.FC<StaffTableProps> = ({ staffMembers, isLoading, onRefresh }) => {
  const { updateUserRole, toggleUserStatus, deleteUser, resetPassword, isUpdatingRole, isTogglingStatus, isDeletingUser } = useStaffManagement();
  const [selectedUser, setSelectedUser] = useState<StaffMember | null>(null);
  const [actionType, setActionType] = useState<'activate' | 'deactivate' | 'role-change' | 'delete' | 'reset-password' | null>(null);
  const [newRole, setNewRole] = useState<UserRole>('field_staff');
  const [newPassword, setNewPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // Generate a secure password
  const generatePassword = () => {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    setNewPassword(password);
    setShowPassword(true); // Show the generated password
  };

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'program_officer':
        return 'default';
      case 'field_staff':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-3 w-3" />;
      case 'program_officer':
        return <UserCheck className="h-3 w-3" />;
      case 'field_staff':
        return <UserX className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const handleRoleChange = (user: StaffMember, role: UserRole) => {
    setSelectedUser(user);
    setNewRole(role);
    setActionType('role-change');
  };

  const handleStatusToggle = (user: StaffMember) => {
    setSelectedUser(user);
    setActionType(user.is_active ? 'deactivate' : 'activate');
  };

  const handleDeleteUser = (user: StaffMember) => {
    setSelectedUser(user);
    setActionType('delete');
  };

  const handleResetPassword = (user: StaffMember) => {
    setSelectedUser(user);
    setActionType('reset-password');
    setNewPassword(''); // Clear password field
    setShowPassword(false); // Hide password by default
  };

  const confirmAction = () => {
    if (!selectedUser) return;

    // Validate password for reset action
    if (actionType === 'reset-password') {
      if (!newPassword || newPassword.length < 6) {
        // Don't proceed if password is invalid
        return;
      }
    }

    if (actionType === 'role-change') {
      updateUserRole({ userId: selectedUser.id, newRole });
    } else if (actionType === 'activate' || actionType === 'deactivate') {
      toggleUserStatus({ userId: selectedUser.id, isActive: actionType === 'activate' });
    } else if (actionType === 'delete') {
      deleteUser(selectedUser.id);
    } else if (actionType === 'reset-password') {
      resetPassword({ userId: selectedUser.id, newPassword });
    }

    setSelectedUser(null);
    setActionType(null);
    setNewPassword(''); // Clear password field
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (staffMembers.length === 0) {
    return (
      <div className="text-center py-8">
        <UserX className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No staff members found</h3>
        <p className="text-gray-500">Try adjusting your filters or add new staff members.</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Division</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead className="w-[50px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {staffMembers.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium">{member.name}</div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <Mail className="h-3 w-3 mr-1" />
                      {member.email}
                    </div>
                    {member.requires_password_change && (
                      <Badge variant="outline" className="text-xs">
                        <Key className="h-3 w-3 mr-1" />
                        Password Reset Required
                      </Badge>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant={getRoleBadgeVariant(member.role)} className="flex items-center w-fit">
                    {getRoleIcon(member.role)}
                    <span className="ml-1 capitalize">{member.role.replace('_', ' ')}</span>
                  </Badge>
                </TableCell>
                
                <TableCell>
                  {member.division_name ? (
                    <div className="flex items-center text-sm">
                      <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                      {member.division_name}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">Not assigned</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    {member.phone && (
                      <div className="text-sm flex items-center">
                        <Phone className="h-3 w-3 mr-1 text-gray-400" />
                        {member.phone}
                      </div>
                    )}
                    <div className="text-xs text-gray-500">{member.country}</div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant={member.is_active ? 'default' : 'secondary'}>
                    {member.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm flex items-center">
                    <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                    {formatDate(member.created_at)}
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem onClick={() => handleRoleChange(member, 'admin')}>
                        <Shield className="h-4 w-4 mr-2" />
                        Make Admin
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => handleRoleChange(member, 'program_officer')}>
                        <UserCheck className="h-4 w-4 mr-2" />
                        Make Program Officer
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => handleRoleChange(member, 'field_staff')}>
                        <UserX className="h-4 w-4 mr-2" />
                        Make Field Staff
                      </DropdownMenuItem>
                      
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem
                        onClick={() => handleStatusToggle(member)}
                        className={member.is_active ? 'text-red-600' : 'text-green-600'}
                      >
                        {member.is_active ? (
                          <>
                            <UserX className="h-4 w-4 mr-2" />
                            Deactivate User
                          </>
                        ) : (
                          <>
                            <UserCheck className="h-4 w-4 mr-2" />
                            Activate User
                          </>
                        )}
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      <DropdownMenuItem
                        onClick={() => handleResetPassword(member)}
                        className="text-orange-600"
                      >
                        <Key className="h-4 w-4 mr-2" />
                        Reset Password
                      </DropdownMenuItem>

                      <DropdownMenuItem
                        onClick={() => handleDeleteUser(member)}
                        className="text-red-600"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Delete User
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={!!selectedUser && !!actionType} onOpenChange={() => {
        setSelectedUser(null);
        setActionType(null);
        setNewPassword('');
        setShowPassword(false);
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {actionType === 'role-change' && 'Change User Role'}
              {actionType === 'activate' && 'Activate User'}
              {actionType === 'deactivate' && 'Deactivate User'}
              {actionType === 'delete' && 'Delete User'}
              {actionType === 'reset-password' && 'Reset Password'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {actionType === 'role-change' && (
                <>
                  Are you sure you want to change {selectedUser?.name}'s role to{' '}
                  <strong>{newRole.replace('_', ' ')}</strong>? This will affect their access permissions.
                </>
              )}
              {actionType === 'activate' && (
                <>
                  Are you sure you want to activate {selectedUser?.name}? They will regain access to the system.
                </>
              )}
              {actionType === 'deactivate' && (
                <>
                  Are you sure you want to deactivate {selectedUser?.name}? They will lose access to the system.
                </>
              )}
              {actionType === 'delete' && (
                <>
                  Are you sure you want to permanently delete {selectedUser?.name}? This action cannot be undone and will remove all their data from the system.
                </>
              )}
              {actionType === 'reset-password' && (
                <div className="space-y-4">
                  <p>Enter a new password for {selectedUser?.name}. They will need to use this password to log in.</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">New Password</label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={generatePassword}
                        className="text-xs"
                      >
                        Generate Password
                      </Button>
                    </div>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder="Enter new password (min 6 characters)"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    {newPassword && newPassword.length < 6 && (
                      <p className="text-sm text-red-600">Password must be at least 6 characters long</p>
                    )}
                  </div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setNewPassword('');
              setShowPassword(false);
            }}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmAction}
              disabled={
                isUpdatingRole ||
                isTogglingStatus ||
                isDeletingUser ||
                (actionType === 'reset-password' && (!newPassword || newPassword.length < 6))
              }
              className={actionType === 'delete' ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              {(isUpdatingRole || isTogglingStatus || isDeletingUser) ? 'Processing...' : 'Confirm'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default StaffTable;
