/**
 * <PERSON><PERSON>t to reset a user's password using Supabase Admin API
 * Usage: node scripts/reset-password.js
 */

import { createClient } from '@supabase/supabase-js';

// Use the same configuration as the client
const supabaseUrl = "https://bygrspebofyofymivmib.supabase.co";
// Service role key for admin operations
const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTAzMjE4OCwiZXhwIjoyMDY0NjA4MTg4fQ.d9HbH3fF-tBa8v7HBAR5XnFOghjorx_bAVQh0ZaeDf4";

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function resetUserPassword(userId, newPassword) {
  try {
    console.log(`Resetting password for user ID: ${userId}`);

    // First, let's verify the user exists
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError) {
      console.error('Error fetching user:', userError);
      return false;
    }

    if (!userData.user) {
      console.error('User not found');
      return false;
    }

    console.log(`Found user: ${userData.user.email}`);

    // Now try to update the password
    const { data, error } = await supabase.auth.admin.updateUserById(userId, {
      password: newPassword
    });

    if (error) {
      console.error('Error resetting password:', error);
      return false;
    }

    console.log('Password reset successfully!');
    console.log(`New password: ${newPassword}`);
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// User details for Anita Kwihoreze
const targetUser = {
  id: "88731d07-ee6e-49ee-bf3a-3e1002441abf",
  name: "Anita Kwihoreze",
  email: "<EMAIL>",
  role: "program_officer"
};

const newPassword = "Xzt4q87m";

async function main() {
  console.log('=== Password Reset Script ===');
  console.log(`Target User: ${targetUser.name} (${targetUser.email})`);
  console.log(`New Password: ${newPassword}`);
  console.log('');

  const success = await resetUserPassword(targetUser.id, newPassword);
  
  if (success) {
    console.log('✅ Password reset completed successfully!');
    console.log('');
    console.log('User can now log in with:');
    console.log(`Email: ${targetUser.email}`);
    console.log(`Password: ${newPassword}`);
  } else {
    console.log('❌ Password reset failed!');
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
