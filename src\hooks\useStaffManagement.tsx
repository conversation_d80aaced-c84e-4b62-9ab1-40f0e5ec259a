import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type UserRole = Database['public']['Enums']['user_role'];

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  division_id: string | null;
  division_name: string | null;
  phone: string | null;
  country: string;
  is_active: boolean;
  created_at: string;
}

interface CreateUserData {
  email: string;
  name: string;
  role: UserRole;
  division_id?: string;
  phone?: string;
  password?: string;
}

interface UpdateUserData {
  id: string;
  name?: string;
  role?: UserRole;
  division_id?: string;
  phone?: string;
  is_active?: boolean;
}

export const useStaffManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all staff members using database query to get emails
  const {
    data: staffMembers,
    isLoading: isLoadingStaff,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ['staff-members'],
    queryFn: async (): Promise<StaffMember[]> => {
      console.log('Fetching all staff members...');

      // Use direct SQL query to get staff with emails
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          name,
          role,
          division_id,
          phone,
          country,
          is_active,
          created_at,
          administrative_divisions (
            district,
            sub_county
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching profiles:', error);
        throw new Error(`Failed to fetch staff: ${error.message}`);
      }

      // Get emails using a separate query to auth.users via SQL
      const { data: emailData, error: emailError } = await supabase
        .from('profiles')
        .select(`
          id,
          auth_users:id (
            email
          )
        `);

      // Create email lookup map
      const emailMap = new Map();
      if (!emailError && emailData) {
        // Since we can't directly join auth.users, let's try a different approach
        // Get emails using RPC function
        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_staff_with_emails');
          if (!rpcError && rpcData) {
            rpcData.forEach((row: any) => {
              emailMap.set(row.id, row.email);
            });
          }
        } catch (rpcErr) {
          console.warn('RPC call failed, emails will not be available:', rpcErr);
        }
      }

      // Transform the data
      const staffWithEmails: StaffMember[] = (data || []).map(profile => {
        const division = profile.administrative_divisions;
        const email = emailMap.get(profile.id) || 'No email';

        return {
          id: profile.id,
          name: profile.name,
          email: email,
          role: profile.role,
          division_id: profile.division_id,
          division_name: division ? `${division.district}, ${division.sub_county}` : null,
          phone: profile.phone,
          country: profile.country,
          is_active: profile.is_active ?? true,
          created_at: profile.created_at || new Date().toISOString(),
        };
      });

      console.log('✅ Staff members fetched successfully:', staffWithEmails.length, 'members');
      return staffWithEmails;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Create new user with auth and profile
  const createUser = useMutation({
    mutationFn: async (userData: CreateUserData) => {
      console.log('Creating new user:', userData.email);
      
      // Generate a temporary password if not provided
      const tempPassword = userData.password || `TempPass${Math.random().toString(36).slice(-8)}!`;
      
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: tempPassword,
        email_confirm: true,
      });

      if (authError) {
        console.error('Error creating auth user:', authError);
        throw new Error(`Failed to create user: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('No user data returned from auth creation');
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          name: userData.name,
          role: userData.role,
          division_id: userData.division_id || null,
          phone: userData.phone || null,
          country: 'Uganda',
          is_active: true,
        });

      if (profileError) {
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        console.error('Error creating profile:', profileError);
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      return { user: authData.user, tempPassword };
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: `User created successfully. Temporary password: ${data.tempPassword}`,
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user profile
  const updateUser = useMutation({
    mutationFn: async (userData: UpdateUserData) => {
      console.log('Updating user:', userData.id);
      
      const { error } = await supabase
        .from('profiles')
        .update({
          name: userData.name,
          role: userData.role,
          division_id: userData.division_id,
          phone: userData.phone,
          is_active: userData.is_active,
        })
        .eq('id', userData.id);

      if (error) {
        console.error('Error updating user:', error);
        throw new Error(`Failed to update user: ${error.message}`);
      }

      return userData;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reset user password
  const resetPassword = useMutation({
    mutationFn: async ({ userId, newPassword }: { userId: string; newPassword?: string }) => {
      console.log('Resetting password for user:', userId);

      // Use provided password or generate a new temporary password
      const passwordToSet = newPassword || `TempPass${Math.random().toString(36).slice(-8)}!`;

      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: passwordToSet,
      });

      if (error) {
        console.error('Error resetting password:', error);
        throw new Error(`Failed to reset password: ${error.message}`);
      }

      return { newPassword: passwordToSet };
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: `Password reset successfully. New password: ${data.newPassword}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete user (remove from auth and profiles)
  const deleteUser = useMutation({
    mutationFn: async (userId: string) => {
      console.log('Deleting user:', userId);
      
      // Delete from auth (this will cascade to profile due to foreign key)
      const { error: authError } = await supabase.auth.admin.deleteUser(userId);

      if (authError) {
        console.error('Error deleting user from auth:', authError);
        throw new Error(`Failed to delete user: ${authError.message}`);
      }

      // Also delete from profiles table to be sure
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        console.warn('Error deleting profile (may already be deleted):', profileError);
      }

      return userId;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    // Data
    staffMembers: staffMembers || [],
    
    // Loading states
    isLoadingStaff,
    
    // Mutations
    createUser,
    updateUser,
    resetPassword,
    deleteUser,
    
    // Refetch functions
    refetchStaff,
  };
};
